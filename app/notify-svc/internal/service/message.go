// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "halalplus/app/notify-svc/api/message/v1"
)

type (
	IMessage interface {
		HasUnread(ctx context.Context, req *v1.HasUnreadReq) (res *v1.HasUnreadData, err error)
		MessageList(ctx context.Context, req *v1.MessageListReq) (res *v1.MessageListData, err error)
		MessageOne(ctx context.Context, req *v1.MessageOneReq) (res *v1.MessageListItem, err error)
		MessageDelete(ctx context.Context, req *v1.MessageDeleteReq) (res *v1.MessageDeleteRes, err error)
	}
)

var (
	localMessage IMessage
)

func Message() IMessage {
	if localMessage == nil {
		panic("implement not found for interface IMessage, forgot register?")
	}
	return localMessage
}

func RegisterMessage(i IMessage) {
	localMessage = i
}
