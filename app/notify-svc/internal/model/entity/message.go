// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// Message is the golang structure for table message.
type Message struct {
	Id          uint   `json:"id"          orm:"id"           description:""`             //
	MessageType int    `json:"messageType" orm:"message_type" description:"消息类型 1群发 2私聊"` // 消息类型 1群发 2私聊
	SenderId    int    `json:"senderId"    orm:"sender_id"    description:"发送人员"`         // 发送人员
	Title       string `json:"title"       orm:"title"        description:"标题"`           // 标题
	Content     string `json:"content"     orm:"content"      description:"内容"`           // 内容
	CreateTime  int64  `json:"createTime"  orm:"create_time"  description:"创建时间"`         // 创建时间
	UpdateTime  int64  `json:"updateTime"  orm:"update_time"  description:"更新时间"`         // 更新时间
}
