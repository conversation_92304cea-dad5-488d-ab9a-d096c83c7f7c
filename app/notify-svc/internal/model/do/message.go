// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Message is the golang structure of table message for DAO operations like Where/Data.
type Message struct {
	g.Meta      `orm:"table:message, do:true"`
	Id          interface{} //
	MessageType interface{} // 消息类型 1群发 2私聊
	SenderId    interface{} // 发送人员
	Title       interface{} // 标题
	Content     interface{} // 内容
	CreateTime  interface{} // 创建时间
	UpdateTime  interface{} // 更新时间
}
