package message

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/api/common"
	v1 "halalplus/app/notify-svc/api/message/v1"
	"halalplus/app/notify-svc/internal/dao"
	"halalplus/app/notify-svc/internal/model/do"
	"halalplus/app/notify-svc/internal/model/entity"
	"halalplus/app/notify-svc/internal/service"
	"halalplus/utility/token"
)

type sMessage struct{}

func init() {
	service.RegisterMessage(New())
}

func New() service.IMessage {
	return &sMessage{}
}

func (s *sMessage) HasUnread(ctx context.Context, req *v1.HasUnreadReq) (res *v1.HasUnreadData, err error) {
	userId, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}

	// 取出上次最大的广播messageId
	value, err := dao.MessageUserBroadcast.Ctx(ctx).
		Where(dao.MessageUserBroadcast.Columns().UserId, userId).
		Fields(dao.MessageUserBroadcast.Columns().LastBroadcastId).Value()
	if err != nil {
		return nil, err
	}
	if err != nil {
		return nil, err
	}

	lastId := value.Int()
	// 查询是否有最新的群发消息。
	var entryList []*entity.Message
	err = dao.Message.Ctx(ctx).
		WhereGT(dao.Message.Columns().Id, lastId).
		Where(dao.Message.Columns().MessageType, "1").
		Scan(&entryList)
	if err != nil {
		return nil, err
	}
	// 有群发消息后插入message_user表,更新 last_broadcast_id
	if len(entryList) > 0 {
		var maxId uint
		var messageUsers []*entity.MessageUser
		for _, entry := range entryList {
			messageUsers = append(messageUsers, &entity.MessageUser{
				UserId:    uint(userId),
				MessageId: entry.Id,
			})
			if entry.Id > maxId {
				maxId = entry.Id
			}
		}
		err = dao.MessageUser.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			_, err = tx.Model(dao.MessageUser.Table()).Ctx(ctx).Insert(messageUsers)
			if err != nil {
				return err
			}

			_, err = tx.Model(dao.MessageUserBroadcast.Table()).Ctx(ctx).
				Data(g.Map{
					dao.MessageUserBroadcast.Columns().LastBroadcastId: maxId,
				}).
				Where(dao.MessageUserBroadcast.Columns().UserId, userId).
				Update()
			return err
		})
		if err != nil {
			return nil, err
		}

	}
	// 查询是否有未读消息
	count, err := dao.MessageUser.Ctx(ctx).
		Where(dao.MessageUser.Columns().UserId, userId).
		Where(dao.MessageUser.Columns().IsRead, 0).
		Count()
	if err != nil {
		return nil, err
	}
	res = &v1.HasUnreadData{
		HasUnread: count > 0,
	}
	return
}

func (s *sMessage) MessageList(ctx context.Context, req *v1.MessageListReq) (res *v1.MessageListData, err error) {
	userId, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}
	mu := dao.MessageUser.Table()
	m := dao.Message.Table()

	// 先查询message user表。分页 获取message id
	md := dao.MessageUser.Ctx(ctx).LeftJoin(m, fmt.Sprintf("%s.id = %s.message_id", m, mu)).
		Where(mu+".user_id", userId).
		Order(mu + ".id DESC")
	count, err := md.Count()
	if err != nil {
		return nil, err
	}

	var messageListItems []*v1.MessageListItem
	err = md.Fields(
		fmt.Sprintf("%s.id", mu),
		fmt.Sprintf("%s.message_type", m),
		fmt.Sprintf("%s.sender_id", m),
		fmt.Sprintf("%s.title", m),
		fmt.Sprintf("%s.is_read", mu),
	).Page(int(req.Page.Page), int(req.Page.Size)).Scan(&messageListItems)
	if err != nil {
		return nil, err
	}

	res = &v1.MessageListData{
		List: messageListItems,
		Page: &common.PageResponse{
			Page:  req.Page.Page,
			Size:  req.Page.Size,
			Total: int32(count),
		},
	}
	return

}

func (s *sMessage) MessageOne(ctx context.Context, req *v1.MessageOneReq) (res *v1.MessageListItem, err error) {
	userId, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}
	mu := dao.MessageUser.Table()
	m := dao.Message.Table()

	md := dao.MessageUser.Ctx(ctx).LeftJoin(m, fmt.Sprintf("%s.id = %s.message_id", m, mu)).
		Where(mu+".user_id", userId).
		Where(mu+".id", req.Id)

	err = md.Fields(
		fmt.Sprintf("%s.id", mu),
		fmt.Sprintf("%s.message_type", m),
		fmt.Sprintf("%s.sender_id", m),
		fmt.Sprintf("%s.title", m),
		fmt.Sprintf("%s.content", m),
		fmt.Sprintf("%s.is_read", mu),
	).Scan(&res)
	if err != nil {
		return nil, err
	}
	_, err = dao.MessageUser.Ctx(ctx).Where(dao.MessageUser.Columns().Id, req.Id).Update(
		do.MessageUser{IsRead: 1})
	if err != nil {
		return nil, err
	}

	return

}

func (s *sMessage) MessageDelete(ctx context.Context, req *v1.MessageDeleteReq) (res *v1.MessageDeleteRes, err error) {
	userId, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}
	_, err = dao.MessageUser.Ctx(ctx).
		Where(dao.MessageUser.Columns().UserId, userId).
		Where(dao.MessageUser.Columns().Id, req.Id).
		Delete()
	return
}
