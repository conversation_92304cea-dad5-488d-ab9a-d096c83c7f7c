syntax = "proto3";

package message.v1;
option go_package = "halalplus/app/notify-svc/api/message/v1;messagev1";
import "common/base.proto";


// 消息列表请求
message MessageListReq {
  common.PageRequest page = 1;  // 分页参数
}
// 消息列表响应
message MessageListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  MessageListData data = 4;
}

message MessageListData {
  common.PageResponse page = 1;  // 分页参数
  repeated  MessageListItem list = 2;
}
message MessageListItem {
  uint32 id = 1;
  uint32 message_type = 2; // 消息类型 1群发 2私聊
  uint32 sender_id = 3; // 发送者ID
  string title = 4; // 标题
  uint64 create_time =  5;
  uint32 is_read = 6; // 是否已读
  string content = 7; // 内容
}
// 是否有未读消息请求
message HasUnreadReq {
}

// 是否有未读消息响应
message HasUnreadRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HasUnreadData data = 4;
}
message HasUnreadData {
  bool has_unread = 1;
}

// 单条消息详情请求
message MessageOneReq {
  uint32 id = 1;
}
// 单条消息详情响应
message MessageOneRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  MessageListItem data = 4;
}
// 删除消息请求
message MessageDeleteReq{
  uint32 id = 1;// id
}
// 删除消息响应
message MessageDeleteRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}
// 消息服务
service MessageService {
  // 信息列表
  rpc MessageList(MessageListReq) returns (MessageListRes);
  //是否有未读消息
  rpc HasUnread(HasUnreadReq) returns (HasUnreadRes);
  // 消息详情
  rpc MessageOne(MessageOneReq) returns (MessageOneRes);
  // 删除消息
  rpc MessageDelete(MessageDeleteReq) returns (MessageDeleteRes);
}