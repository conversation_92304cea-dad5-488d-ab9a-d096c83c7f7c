// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: message/v1/message.proto

package messagev1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 消息列表请求
type MessageListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageListReq) Reset() {
	*x = MessageListReq{}
	mi := &file_message_v1_message_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageListReq) ProtoMessage() {}

func (x *MessageListReq) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageListReq.ProtoReflect.Descriptor instead.
func (*MessageListReq) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{0}
}

func (x *MessageListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 消息列表响应
type MessageListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *MessageListData       `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageListRes) Reset() {
	*x = MessageListRes{}
	mi := &file_message_v1_message_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageListRes) ProtoMessage() {}

func (x *MessageListRes) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageListRes.ProtoReflect.Descriptor instead.
func (*MessageListRes) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{1}
}

func (x *MessageListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *MessageListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *MessageListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MessageListRes) GetData() *MessageListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type MessageListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageResponse   `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	List          []*MessageListItem     `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageListData) Reset() {
	*x = MessageListData{}
	mi := &file_message_v1_message_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageListData) ProtoMessage() {}

func (x *MessageListData) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageListData.ProtoReflect.Descriptor instead.
func (*MessageListData) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{2}
}

func (x *MessageListData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *MessageListData) GetList() []*MessageListItem {
	if x != nil {
		return x.List
	}
	return nil
}

type MessageListItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MessageType   uint32                 `protobuf:"varint,2,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty" dc:"消息类型 1群发 2私聊"` // 消息类型 1群发 2私聊
	SenderId      uint32                 `protobuf:"varint,3,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty" dc:"发送者ID"`                 // 发送者ID
	Title         uint32                 `protobuf:"varint,4,opt,name=title,proto3" json:"title,omitempty" dc:"标题"`                                          // 标题
	CreateTime    uint64                 `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	IsRead        uint32                 `protobuf:"varint,6,opt,name=is_read,json=isRead,proto3" json:"is_read,omitempty" dc:"是否已读"` // 是否已读
	Content       string                 `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty" dc:"内容"`                // 内容
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageListItem) Reset() {
	*x = MessageListItem{}
	mi := &file_message_v1_message_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageListItem) ProtoMessage() {}

func (x *MessageListItem) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageListItem.ProtoReflect.Descriptor instead.
func (*MessageListItem) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{3}
}

func (x *MessageListItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MessageListItem) GetMessageType() uint32 {
	if x != nil {
		return x.MessageType
	}
	return 0
}

func (x *MessageListItem) GetSenderId() uint32 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *MessageListItem) GetTitle() uint32 {
	if x != nil {
		return x.Title
	}
	return 0
}

func (x *MessageListItem) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *MessageListItem) GetIsRead() uint32 {
	if x != nil {
		return x.IsRead
	}
	return 0
}

func (x *MessageListItem) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// 是否有未读消息请求
type HasUnreadReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HasUnreadReq) Reset() {
	*x = HasUnreadReq{}
	mi := &file_message_v1_message_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HasUnreadReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasUnreadReq) ProtoMessage() {}

func (x *HasUnreadReq) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasUnreadReq.ProtoReflect.Descriptor instead.
func (*HasUnreadReq) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{4}
}

// 是否有未读消息响应
type HasUnreadRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *HasUnreadData         `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HasUnreadRes) Reset() {
	*x = HasUnreadRes{}
	mi := &file_message_v1_message_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HasUnreadRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasUnreadRes) ProtoMessage() {}

func (x *HasUnreadRes) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasUnreadRes.ProtoReflect.Descriptor instead.
func (*HasUnreadRes) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{5}
}

func (x *HasUnreadRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HasUnreadRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HasUnreadRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HasUnreadRes) GetData() *HasUnreadData {
	if x != nil {
		return x.Data
	}
	return nil
}

type HasUnreadData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HasUnread     bool                   `protobuf:"varint,1,opt,name=has_unread,json=hasUnread,proto3" json:"has_unread,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HasUnreadData) Reset() {
	*x = HasUnreadData{}
	mi := &file_message_v1_message_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HasUnreadData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasUnreadData) ProtoMessage() {}

func (x *HasUnreadData) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasUnreadData.ProtoReflect.Descriptor instead.
func (*HasUnreadData) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{6}
}

func (x *HasUnreadData) GetHasUnread() bool {
	if x != nil {
		return x.HasUnread
	}
	return false
}

// 单条消息详情请求
type MessageOneReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageOneReq) Reset() {
	*x = MessageOneReq{}
	mi := &file_message_v1_message_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageOneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageOneReq) ProtoMessage() {}

func (x *MessageOneReq) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageOneReq.ProtoReflect.Descriptor instead.
func (*MessageOneReq) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{7}
}

func (x *MessageOneReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 单条消息详情响应
type MessageOneRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *MessageListItem       `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageOneRes) Reset() {
	*x = MessageOneRes{}
	mi := &file_message_v1_message_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageOneRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageOneRes) ProtoMessage() {}

func (x *MessageOneRes) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageOneRes.ProtoReflect.Descriptor instead.
func (*MessageOneRes) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{8}
}

func (x *MessageOneRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *MessageOneRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *MessageOneRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MessageOneRes) GetData() *MessageListItem {
	if x != nil {
		return x.Data
	}
	return nil
}

// 删除消息请求
type MessageDeleteReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"id"` // id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageDeleteReq) Reset() {
	*x = MessageDeleteReq{}
	mi := &file_message_v1_message_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageDeleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageDeleteReq) ProtoMessage() {}

func (x *MessageDeleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageDeleteReq.ProtoReflect.Descriptor instead.
func (*MessageDeleteReq) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{9}
}

func (x *MessageDeleteReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 删除消息响应
type MessageDeleteRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageDeleteRes) Reset() {
	*x = MessageDeleteRes{}
	mi := &file_message_v1_message_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageDeleteRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageDeleteRes) ProtoMessage() {}

func (x *MessageDeleteRes) ProtoReflect() protoreflect.Message {
	mi := &file_message_v1_message_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageDeleteRes.ProtoReflect.Descriptor instead.
func (*MessageDeleteRes) Descriptor() ([]byte, []int) {
	return file_message_v1_message_proto_rawDescGZIP(), []int{10}
}

func (x *MessageDeleteRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *MessageDeleteRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *MessageDeleteRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_message_v1_message_proto protoreflect.FileDescriptor

const file_message_v1_message_proto_rawDesc = "" +
	"\n" +
	"\x18message/v1/message.proto\x12\n" +
	"message.v1\x1a\x11common/base.proto\"9\n" +
	"\x0eMessageListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\"\x8c\x01\n" +
	"\x0eMessageListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12/\n" +
	"\x04data\x18\x04 \x01(\v2\x1b.message.v1.MessageListDataR\x04data\"l\n" +
	"\x0fMessageListData\x12(\n" +
	"\x04page\x18\x01 \x01(\v2\x14.common.PageResponseR\x04page\x12/\n" +
	"\x04list\x18\x02 \x03(\v2\x1b.message.v1.MessageListItemR\x04list\"\xcb\x01\n" +
	"\x0fMessageListItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12!\n" +
	"\fmessage_type\x18\x02 \x01(\rR\vmessageType\x12\x1b\n" +
	"\tsender_id\x18\x03 \x01(\rR\bsenderId\x12\x14\n" +
	"\x05title\x18\x04 \x01(\rR\x05title\x12\x1f\n" +
	"\vcreate_time\x18\x05 \x01(\x04R\n" +
	"createTime\x12\x17\n" +
	"\ais_read\x18\x06 \x01(\rR\x06isRead\x12\x18\n" +
	"\acontent\x18\a \x01(\tR\acontent\"\x0e\n" +
	"\fHasUnreadReq\"\x88\x01\n" +
	"\fHasUnreadRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12-\n" +
	"\x04data\x18\x04 \x01(\v2\x19.message.v1.HasUnreadDataR\x04data\".\n" +
	"\rHasUnreadData\x12\x1d\n" +
	"\n" +
	"has_unread\x18\x01 \x01(\bR\thasUnread\"\x1f\n" +
	"\rMessageOneReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"\x8b\x01\n" +
	"\rMessageOneRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12/\n" +
	"\x04data\x18\x04 \x01(\v2\x1b.message.v1.MessageListItemR\x04data\"\"\n" +
	"\x10MessageDeleteReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"]\n" +
	"\x10MessageDeleteRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error2\xa9\x02\n" +
	"\x0eMessageService\x12E\n" +
	"\vMessageList\x12\x1a.message.v1.MessageListReq\x1a\x1a.message.v1.MessageListRes\x12?\n" +
	"\tHasUnread\x12\x18.message.v1.HasUnreadReq\x1a\x18.message.v1.HasUnreadRes\x12B\n" +
	"\n" +
	"MessageOne\x12\x19.message.v1.MessageOneReq\x1a\x19.message.v1.MessageOneRes\x12K\n" +
	"\rMessageDelete\x12\x1c.message.v1.MessageDeleteReq\x1a\x1c.message.v1.MessageDeleteResB3Z1halalplus/app/notify-svc/api/message/v1;messagev1b\x06proto3"

var (
	file_message_v1_message_proto_rawDescOnce sync.Once
	file_message_v1_message_proto_rawDescData []byte
)

func file_message_v1_message_proto_rawDescGZIP() []byte {
	file_message_v1_message_proto_rawDescOnce.Do(func() {
		file_message_v1_message_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_message_v1_message_proto_rawDesc), len(file_message_v1_message_proto_rawDesc)))
	})
	return file_message_v1_message_proto_rawDescData
}

var file_message_v1_message_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_message_v1_message_proto_goTypes = []any{
	(*MessageListReq)(nil),      // 0: message.v1.MessageListReq
	(*MessageListRes)(nil),      // 1: message.v1.MessageListRes
	(*MessageListData)(nil),     // 2: message.v1.MessageListData
	(*MessageListItem)(nil),     // 3: message.v1.MessageListItem
	(*HasUnreadReq)(nil),        // 4: message.v1.HasUnreadReq
	(*HasUnreadRes)(nil),        // 5: message.v1.HasUnreadRes
	(*HasUnreadData)(nil),       // 6: message.v1.HasUnreadData
	(*MessageOneReq)(nil),       // 7: message.v1.MessageOneReq
	(*MessageOneRes)(nil),       // 8: message.v1.MessageOneRes
	(*MessageDeleteReq)(nil),    // 9: message.v1.MessageDeleteReq
	(*MessageDeleteRes)(nil),    // 10: message.v1.MessageDeleteRes
	(*common.PageRequest)(nil),  // 11: common.PageRequest
	(*common.Error)(nil),        // 12: common.Error
	(*common.PageResponse)(nil), // 13: common.PageResponse
}
var file_message_v1_message_proto_depIdxs = []int32{
	11, // 0: message.v1.MessageListReq.page:type_name -> common.PageRequest
	12, // 1: message.v1.MessageListRes.error:type_name -> common.Error
	2,  // 2: message.v1.MessageListRes.data:type_name -> message.v1.MessageListData
	13, // 3: message.v1.MessageListData.page:type_name -> common.PageResponse
	3,  // 4: message.v1.MessageListData.list:type_name -> message.v1.MessageListItem
	12, // 5: message.v1.HasUnreadRes.error:type_name -> common.Error
	6,  // 6: message.v1.HasUnreadRes.data:type_name -> message.v1.HasUnreadData
	12, // 7: message.v1.MessageOneRes.error:type_name -> common.Error
	3,  // 8: message.v1.MessageOneRes.data:type_name -> message.v1.MessageListItem
	12, // 9: message.v1.MessageDeleteRes.error:type_name -> common.Error
	0,  // 10: message.v1.MessageService.MessageList:input_type -> message.v1.MessageListReq
	4,  // 11: message.v1.MessageService.HasUnread:input_type -> message.v1.HasUnreadReq
	7,  // 12: message.v1.MessageService.MessageOne:input_type -> message.v1.MessageOneReq
	9,  // 13: message.v1.MessageService.MessageDelete:input_type -> message.v1.MessageDeleteReq
	1,  // 14: message.v1.MessageService.MessageList:output_type -> message.v1.MessageListRes
	5,  // 15: message.v1.MessageService.HasUnread:output_type -> message.v1.HasUnreadRes
	8,  // 16: message.v1.MessageService.MessageOne:output_type -> message.v1.MessageOneRes
	10, // 17: message.v1.MessageService.MessageDelete:output_type -> message.v1.MessageDeleteRes
	14, // [14:18] is the sub-list for method output_type
	10, // [10:14] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_message_v1_message_proto_init() }
func file_message_v1_message_proto_init() {
	if File_message_v1_message_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_message_v1_message_proto_rawDesc), len(file_message_v1_message_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_message_v1_message_proto_goTypes,
		DependencyIndexes: file_message_v1_message_proto_depIdxs,
		MessageInfos:      file_message_v1_message_proto_msgTypes,
	}.Build()
	File_message_v1_message_proto = out.File
	file_message_v1_message_proto_goTypes = nil
	file_message_v1_message_proto_depIdxs = nil
}
