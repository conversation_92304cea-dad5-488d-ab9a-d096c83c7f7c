// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: message/v1/message.proto

package messagev1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MessageService_MessageList_FullMethodName   = "/message.v1.MessageService/MessageList"
	MessageService_HasUnread_FullMethodName     = "/message.v1.MessageService/HasUnread"
	MessageService_MessageOne_FullMethodName    = "/message.v1.MessageService/MessageOne"
	MessageService_MessageDelete_FullMethodName = "/message.v1.MessageService/MessageDelete"
)

// MessageServiceClient is the client API for MessageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 消息服务
type MessageServiceClient interface {
	// 信息列表
	MessageList(ctx context.Context, in *MessageListReq, opts ...grpc.CallOption) (*MessageListRes, error)
	// 是否有未读消息
	HasUnread(ctx context.Context, in *HasUnreadReq, opts ...grpc.CallOption) (*HasUnreadRes, error)
	// 消息详情
	MessageOne(ctx context.Context, in *MessageOneReq, opts ...grpc.CallOption) (*MessageOneRes, error)
	// 删除消息
	MessageDelete(ctx context.Context, in *MessageDeleteReq, opts ...grpc.CallOption) (*MessageDeleteRes, error)
}

type messageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageServiceClient(cc grpc.ClientConnInterface) MessageServiceClient {
	return &messageServiceClient{cc}
}

func (c *messageServiceClient) MessageList(ctx context.Context, in *MessageListReq, opts ...grpc.CallOption) (*MessageListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MessageListRes)
	err := c.cc.Invoke(ctx, MessageService_MessageList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) HasUnread(ctx context.Context, in *HasUnreadReq, opts ...grpc.CallOption) (*HasUnreadRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HasUnreadRes)
	err := c.cc.Invoke(ctx, MessageService_HasUnread_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) MessageOne(ctx context.Context, in *MessageOneReq, opts ...grpc.CallOption) (*MessageOneRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MessageOneRes)
	err := c.cc.Invoke(ctx, MessageService_MessageOne_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) MessageDelete(ctx context.Context, in *MessageDeleteReq, opts ...grpc.CallOption) (*MessageDeleteRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MessageDeleteRes)
	err := c.cc.Invoke(ctx, MessageService_MessageDelete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageServiceServer is the server API for MessageService service.
// All implementations must embed UnimplementedMessageServiceServer
// for forward compatibility.
//
// 消息服务
type MessageServiceServer interface {
	// 信息列表
	MessageList(context.Context, *MessageListReq) (*MessageListRes, error)
	// 是否有未读消息
	HasUnread(context.Context, *HasUnreadReq) (*HasUnreadRes, error)
	// 消息详情
	MessageOne(context.Context, *MessageOneReq) (*MessageOneRes, error)
	// 删除消息
	MessageDelete(context.Context, *MessageDeleteReq) (*MessageDeleteRes, error)
	mustEmbedUnimplementedMessageServiceServer()
}

// UnimplementedMessageServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMessageServiceServer struct{}

func (UnimplementedMessageServiceServer) MessageList(context.Context, *MessageListReq) (*MessageListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MessageList not implemented")
}
func (UnimplementedMessageServiceServer) HasUnread(context.Context, *HasUnreadReq) (*HasUnreadRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HasUnread not implemented")
}
func (UnimplementedMessageServiceServer) MessageOne(context.Context, *MessageOneReq) (*MessageOneRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MessageOne not implemented")
}
func (UnimplementedMessageServiceServer) MessageDelete(context.Context, *MessageDeleteReq) (*MessageDeleteRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MessageDelete not implemented")
}
func (UnimplementedMessageServiceServer) mustEmbedUnimplementedMessageServiceServer() {}
func (UnimplementedMessageServiceServer) testEmbeddedByValue()                        {}

// UnsafeMessageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageServiceServer will
// result in compilation errors.
type UnsafeMessageServiceServer interface {
	mustEmbedUnimplementedMessageServiceServer()
}

func RegisterMessageServiceServer(s grpc.ServiceRegistrar, srv MessageServiceServer) {
	// If the following call pancis, it indicates UnimplementedMessageServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MessageService_ServiceDesc, srv)
}

func _MessageService_MessageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).MessageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageService_MessageList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).MessageList(ctx, req.(*MessageListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_HasUnread_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HasUnreadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).HasUnread(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageService_HasUnread_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).HasUnread(ctx, req.(*HasUnreadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_MessageOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageOneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).MessageOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageService_MessageOne_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).MessageOne(ctx, req.(*MessageOneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_MessageDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).MessageDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageService_MessageDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).MessageDelete(ctx, req.(*MessageDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageService_ServiceDesc is the grpc.ServiceDesc for MessageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "message.v1.MessageService",
	HandlerType: (*MessageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MessageList",
			Handler:    _MessageService_MessageList_Handler,
		},
		{
			MethodName: "HasUnread",
			Handler:    _MessageService_HasUnread_Handler,
		},
		{
			MethodName: "MessageOne",
			Handler:    _MessageService_MessageOne_Handler,
		},
		{
			MethodName: "MessageDelete",
			Handler:    _MessageService_MessageDelete_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "message/v1/message.proto",
}
